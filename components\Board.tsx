'use client'

import { DuckCard } from '@/lib/types'
import Card from './Card'

type Props = {
  board: (DuckCard | null)[]
  onTileClick: (index: number) => void
  onAbilityClick?: (index: number) => void
}

export default function Board({ board, onTileClick, onAbilityClick }: Props) {
  return (
    <div className="grid grid-cols-3 grid-rows-3 gap-3 w-fit mx-auto p-4 bg-gradient-to-br from-green-100 to-green-200 rounded-xl shadow-2xl border-4 border-green-300 -mt-20">
      {board.map((card, index) => (
        <div
          key={index}
          onClick={() => onTileClick(index)}
          className="w-24 h-32 border-2 border-dashed border-gray-400 rounded-lg flex items-center justify-center bg-white/50 hover:bg-yellow-100/70 transition-all duration-200 cursor-pointer shadow-inner"
        >
          {card ? (
            <div className="scale-75">
              <Card
                card={card}
                showAbilityIndicator={true}
                onAbilityClick={() => onAbilityClick?.(index)}
              />
            </div>
          ) : (
            <div className="text-gray-400 text-xl font-bold">+</div>
          )}
        </div>
      ))}
    </div>
  )
}
