'use client'

import { DuckCard } from '@/lib/types'
import Card from './Card'

type Props = {
  board: (DuckCard | null)[]
  onTileClick: (index: number) => void
}

export default function Board({ board, onTileClick }: Props) {
  return (
    <div className="grid grid-cols-3 grid-rows-3 gap-2 w-fit mx-auto">
      {board.map((card, index) => (
        <div
          key={index}
          onClick={() => onTileClick(index)}
          className="w-24 h-24 border border-gray-300 rounded-md flex items-center justify-center bg-gray-50 hover:bg-yellow-100 transition cursor-pointer"
        >
          {card ? (
            <Card card={card} />
          ) : (
            <span className="text-gray-300 text-sm">+</span>
          )}
        </div>
      ))}
    </div>
  )
}
