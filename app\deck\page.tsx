'use client'

import { useState } from 'react'
import { allDuckCards, defaultPlayerDeck } from '@/lib/cards'
import { DuckCard } from '@/lib/types'
import Card from '@/components/Card'
import Link from 'next/link'

export default function DeckPage() {
  const [selectedDeck, setSelectedDeck] = useState<DuckCard[]>(defaultPlayerDeck)
  const [selectedCard, setSelectedCard] = useState<DuckCard | null>(null)

  const addCardToDeck = (card: DuckCard) => {
    if (selectedDeck.length < 5) {
      setSelectedDeck([...selectedDeck, { ...card, owner: 'player' }])
    }
  }

  const removeCardFromDeck = (index: number) => {
    setSelectedDeck(selectedDeck.filter((_, i) => i !== index))
  }

  const resetDeck = () => {
    setSelectedDeck([])
  }

  const saveDeck = () => {
    // For now, just store in localStorage
    localStorage.setItem('playerDeck', JSON.stringify(selectedDeck))
    alert('Deck saved! Your custom deck will be used in the next game.')
  }

  const groupedCards = allDuckCards.reduce((acc, card) => {
    if (!acc[card.class]) acc[card.class] = []
    acc[card.class].push(card)
    return acc
  }, {} as Record<string, DuckCard[]>)

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-green-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">🃏 Deck Builder</h1>
          <p className="text-lg text-gray-600 mb-6">
            Choose 5 ducks to create your perfect deck. Each duck brings unique abilities to the battlefield!
          </p>

          <div className="flex gap-4 justify-center">
            <Link
              href="/"
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition"
            >
              ← Home
            </Link>
            <Link
              href="/play"
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition"
            >
              🎮 Play Game
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Current Deck */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold mb-4 text-center">Your Deck ({selectedDeck.length}/5)</h2>

              <div className="space-y-3 mb-6">
                {selectedDeck.map((card, index) => (
                  <div key={`${card.id}-${index}`} className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div className="scale-50 origin-left">
                      <Card card={card} size="small" />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-sm">{card.name}</h3>
                      <p className="text-xs text-gray-600">{card.ability.name}</p>
                    </div>
                    <button
                      onClick={() => removeCardFromDeck(index)}
                      className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition"
                    >
                      Remove
                    </button>
                  </div>
                ))}

                {/* Empty slots */}
                {Array.from({ length: 5 - selectedDeck.length }).map((_, index) => (
                  <div key={`empty-${index}`} className="h-16 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center text-gray-400">
                    Empty Slot
                  </div>
                ))}
              </div>

              <div className="space-y-2">
                <button
                  onClick={saveDeck}
                  disabled={selectedDeck.length !== 5}
                  className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  💾 Save Deck
                </button>
                <button
                  onClick={resetDeck}
                  className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition"
                >
                  🗑️ Clear Deck
                </button>
              </div>
            </div>
          </div>

          {/* Card Collection */}
          <div className="lg:col-span-2">
            <div className="bg-white/80 backdrop-blur-sm rounded-xl p-6 shadow-lg">
              <h2 className="text-2xl font-bold mb-6 text-center">Available Cards</h2>

              {Object.entries(groupedCards).map(([className, cards]) => (
                <div key={className} className="mb-8">
                  <h3 className="text-xl font-semibold mb-4 capitalize">{className} Ducks</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {cards.map((card) => (
                      <div key={card.id} className="relative">
                        <button
                          onClick={() => {
                            setSelectedCard(card)
                            if (selectedDeck.length < 5) {
                              addCardToDeck(card)
                            }
                          }}
                          disabled={selectedDeck.length >= 5}
                          className="w-full hover:scale-105 transition-transform disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <Card card={card} />
                        </button>

                        {selectedDeck.length >= 5 && (
                          <div className="absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center">
                            <span className="text-white font-bold text-sm">Deck Full</span>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Selected Card Details */}
        {selectedCard && (
          <div className="fixed top-4 left-4 p-4 bg-white/95 backdrop-blur rounded-lg max-w-sm shadow-xl border-2 border-purple-400 z-30">
            <h3 className="font-bold text-lg text-gray-800">{selectedCard.name}</h3>
            <p className="text-sm text-gray-600 mt-1">{selectedCard.ability.description}</p>
            <p className="text-xs text-gray-500 mt-2">
              Stats: {selectedCard.top}-{selectedCard.right}-{selectedCard.bottom}-{selectedCard.left} (T-R-B-L)
            </p>
            <button
              onClick={() => setSelectedCard(null)}
              className="mt-2 text-xs text-gray-500 hover:text-gray-700"
            >
              Click to close
            </button>
          </div>
        )}
      </div>
    </div>
  )
}