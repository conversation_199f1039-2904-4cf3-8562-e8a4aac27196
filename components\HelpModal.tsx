'use client'

import { useState } from 'react'

type Props = {
  isOpen: boolean
  onClose: () => void
}

export default function HelpModal({ isOpen, onClose }: Props) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto shadow-2xl">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-800">🦆 How to Play Duck Dominion</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-2xl font-bold"
            >
              ×
            </button>
          </div>

          <div className="space-y-6">
            <section>
              <h3 className="text-lg font-semibold text-gray-700 mb-3">Basic Rules</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  Select a card from your hand and place it on the board
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  Cards flip adjacent enemy cards if your stat &gt; their opposing stat
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  Win by controlling more cards when the game ends!
                </li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-700 mb-3">Duck Abilities</h3>
              <div className="space-y-3">
                <div className="bg-purple-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-purple-700">🪄 Wizard Duck - Spellfire</h4>
                  <p className="text-sm text-gray-600">Reduces the highest stat of one adjacent enemy card by 1</p>
                </div>
                
                <div className="bg-yellow-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-yellow-700">🛡️ Paladin Duck - Shield of Honor</h4>
                  <p className="text-sm text-gray-600">Protects this card from being flipped the first time</p>
                </div>
                
                <div className="bg-red-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-red-700">🗡️ Rogue Duck - Backstab</h4>
                  <p className="text-sm text-gray-600">Guarantees flipping one adjacent enemy card regardless of stats</p>
                </div>
                
                <div className="bg-green-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-green-700">🧠 Cleric Duck - Blessing of Feathers</h4>
                  <p className="text-sm text-gray-600">Increases all adjacent friendly card stats by +1</p>
                </div>
                
                <div className="bg-orange-50 p-3 rounded-lg">
                  <h4 className="font-semibold text-orange-700">🏹 Ranger Duck - Volley Shot</h4>
                  <p className="text-sm text-gray-600">Reduces a random stat of all adjacent enemy cards by 1</p>
                </div>
              </div>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-700 mb-3">Visual Indicators</h3>
              <ul className="space-y-2 text-gray-600">
                <li className="flex items-center">
                  <span className="text-2xl mr-2">🛡️</span>
                  <span>Yellow pulsing shield - Card is protected from flipping</span>
                </li>
                <li className="flex items-center">
                  <span className="text-2xl mr-2">✓</span>
                  <span>Gray checkmark - Ability has been used</span>
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 bg-green-500 rounded mr-2"></span>
                  <span>Green glowing stats - Recently buffed</span>
                </li>
                <li className="flex items-center">
                  <span className="w-4 h-4 bg-red-500 rounded mr-2"></span>
                  <span>Red glowing stats - Recently debuffed</span>
                </li>
              </ul>
            </section>
          </div>

          <div className="mt-8 text-center">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition font-semibold"
            >
              Got it!
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export function HelpButton({ onClick }: { onClick: () => void }) {
  return (
    <button
      onClick={onClick}
      className="fixed top-4 right-4 w-12 h-12 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition shadow-lg flex items-center justify-center text-xl font-bold z-40"
      title="How to Play"
    >
      ?
    </button>
  )
}
