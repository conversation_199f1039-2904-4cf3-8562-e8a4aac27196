export type DuckClass = 'wizard' | 'paladin' | 'rogue' | 'cleric' | 'ranger'

export type DuckCard = {
  id: string
  name: string
  class: DuckClass
  top: number
  right: number
  bottom: number
  left: number
  ability: {
    name: string
    description: string
    trigger: 'onPlay'
    effect: 'flip' | 'buff' | 'shield' | 'steal' | 'spellfire' | 'backstab' | 'volley'
    used?: boolean
  }
  owner: 'player' | 'enemy' | null
  shielded?: boolean
}