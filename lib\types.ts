export type DuckClass = 'wizard' | 'paladin' | 'rogue'

export type DuckCard = {
  id: string
  name: string
  class: DuckClass
  top: number
  right: number
  bottom: number
  left: number
  ability: {
    name: string
    description: string
    trigger: 'onPlay' | 'manual'
    effect: 'flip' | 'buff' | 'shield' | 'steal'
    used?: boolean
  }
  owner: 'player' | 'enemy' | null
  shielded?: boolean
}