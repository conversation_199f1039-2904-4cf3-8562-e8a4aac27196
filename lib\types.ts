export type DuckClass = 'wizard' | 'paladin' | 'rogue' | 'cleric' | 'ranger'

export type StatChange = {
  stat: 'top' | 'right' | 'bottom' | 'left'
  type: 'buff' | 'debuff'
  timestamp: number
}

export type DuckCard = {
  id: string
  name: string
  class: DuckClass
  top: number
  right: number
  bottom: number
  left: number
  ability: {
    name: string
    description: string
    trigger: 'onPlay'
    effect: 'flip' | 'buff' | 'shield' | 'steal' | 'spellfire' | 'backstab' | 'volley'
    used?: boolean
  }
  owner: 'player' | 'enemy' | null
  shielded?: boolean
  statChanges?: StatChange[]
  isFlipping?: boolean
}