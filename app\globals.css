@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-fantasy: var(--font-im-fell-english);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

.card {
  color: #000
}

/* Card flip animation */
@keyframes flip {
  0% { transform: rotateY(0deg); }
  50% { transform: rotateY(90deg); }
  100% { transform: rotateY(0deg); }
}

.animate-flip {
  animation: flip 0.6s ease-in-out;
}

/* Stat change animations */
@keyframes statBuff {
  0% { transform: scale(1); color: #16a34a; }
  50% { transform: scale(1.2); color: #15803d; }
  100% { transform: scale(1); color: #16a34a; }
}

@keyframes statDebuff {
  0% { transform: scale(1); color: #dc2626; }
  50% { transform: scale(1.2); color: #b91c1c; }
  100% { transform: scale(1); color: #dc2626; }
}

.animate-stat-buff {
  animation: statBuff 0.8s ease-in-out;
}

.animate-stat-debuff {
  animation: statDebuff 0.8s ease-in-out;
}

/* Hand card hover effects */
.hand-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hand-card:hover {
  transform: translateY(-2rem) scale(1.05);
  z-index: 50;
}

/* Board slot hover effects */
.board-slot {
  transition: all 0.2s ease-in-out;
}

.board-slot:hover {
  background-color: rgba(255, 235, 59, 0.3);
  transform: scale(1.02);
}

/* Card shadow effects */
.card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Fantasy font for titles and headings */
.fantasy-font {
  font-family: var(--font-im-fell-english), serif;
}

/* Apply fantasy font to specific elements */
h1, h2, h3 {
  font-family: var(--font-im-fell-english), serif;
}

.card-name {
  font-family: var(--font-im-fell-english), serif;
}

.ability-name {
  font-family: var(--font-im-fell-english), serif;
}