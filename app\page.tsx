'use client'

import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-green-50 flex items-center justify-center">
      <div className="text-center space-y-8 p-8">
        {/* Game Title */}
        <div className="space-y-4">
          <h1 className="text-6xl font-bold text-gray-800 mb-4">
            Feather & Fables
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            A strategic duck card game where cunning tactics and magical abilities determine the victor.
            Build your deck, master the board, and claim dominion over the pond!
          </p>
        </div>

        {/* Navigation Buttons */}
        <div className="space-y-4">
          <Link
            href="/play"
            className="inline-block px-8 py-4 bg-blue-600 text-white text-xl font-semibold rounded-lg hover:bg-blue-700 transition-colors shadow-lg hover:shadow-xl"
          >
            🎮 Play Game
          </Link>

          <div className="flex gap-4 justify-center">
            <Link
              href="/deck"
              className="px-6 py-3 bg-purple-600 text-white font-semibold rounded-lg hover:bg-purple-700 transition-colors shadow-md"
            >
              🃏 Build Deck
            </Link>

            <button
              className="px-6 py-3 bg-gray-600 text-white font-semibold rounded-lg hover:bg-gray-700 transition-colors shadow-md opacity-50 cursor-not-allowed"
              disabled
            >
              👤 Profile (Coming Soon)
            </button>
          </div>
        </div>

        {/* Game Features */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto mt-12">
          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🪄</div>
            <h3 className="font-bold text-lg mb-2">Magical Abilities</h3>
            <p className="text-gray-600 text-sm">Each duck has unique powers that trigger when played, from spellfire to divine shields.</p>
          </div>

          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🎯</div>
            <h3 className="font-bold text-lg mb-2">Strategic Gameplay</h3>
            <p className="text-gray-600 text-sm">Master the art of card placement and stat battles to flip enemy cards and control the board.</p>
          </div>

          <div className="bg-white/70 backdrop-blur-sm p-6 rounded-lg shadow-md">
            <div className="text-3xl mb-3">🃏</div>
            <h3 className="font-bold text-lg mb-2">Deck Building</h3>
            <p className="text-gray-600 text-sm">Customize your deck with different duck combinations to create your perfect strategy.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
