import { DuckCard } from '@/lib/types'

type Props = {
  card: <PERSON><PERSON><PERSON>
}

export default function Card({ card }: Props) {
  const ownerColor =
    card.owner === 'player' ? 'bg-blue-100 border-blue-400' :
    card.owner === 'enemy' ? 'bg-red-100 border-red-400' :
    'bg-white border-gray-300'

  return (
    <div className={`relative w-20 h-20 rounded-md border p-1 flex flex-col items-center justify-center text-xs ${ownerColor} card`}>
      <div className="absolute top-1 left-1 font-bold">{card.top}</div>
      <div className="absolute right-1 top-1 font-bold">{card.right}</div>
      <div className="absolute bottom-1 right-1 font-bold">{card.bottom}</div>
      <div className="absolute bottom-1 left-1 font-bold">{card.left}</div>

      <div className="text-center mt-5">
        <p className="font-semibold text-[10px] leading-tight">{card.name}</p>
        <p className="text-[9px] italic">{card.class}</p>
      </div>
    </div>
  )
}
