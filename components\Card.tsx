import { DuckCard } from '@/lib/types'
import { useEffect, useState } from 'react'
import Image from 'next/image'

type Props = {
  card: DuckCard
  showAbilityIndicator?: boolean
  onAbilityClick?: () => void
  size?: 'normal' | 'small'
}

// Map card classes to their artwork
const getCardArtwork = (card: DuckCard): string => {
  const artworkMap: Record<string, string> = {
    'wizard': '/cardArt/quizardv1.jpg',
    'paladin': '/cardArt/quackadinv1.jpg',
    'rogue': '/cardArt/quoguev1.jpg',
    'cleric': '/cardArt/quickricv1.jpg',
    'ranger': '/cardArt/quangerv1.jpg'
  }

  return artworkMap[card.class] || '/cardArt/quizardv1.jpg'
}

export default function Card({ card, showAbilityIndicator = false, onAbilityClick, size = 'normal' }: Props) {
  const [animatingStats, setAnimatingStats] = useState<Set<string>>(new Set())

  // All abilities are now onPlay, so no manual ability buttons needed
  const canUseAbility = false

  // Size variants
  const isSmall = size === 'small'
  const cardClasses = isSmall ? 'w-24 h-32' : 'w-32 h-44'
  const statTextSize = isSmall ? 'text-lg' : 'text-xl'
  const indicatorSize = isSmall ? 'w-4 h-4 text-xs' : 'w-6 h-6 text-sm'

  // Handle stat change animations
  useEffect(() => {
    if (card.statChanges && card.statChanges.length > 0) {
      const recentChanges = card.statChanges.filter(change =>
        Date.now() - change.timestamp < 2000 // Show for 2 seconds
      )

      if (recentChanges.length > 0) {
        const newAnimatingStats = new Set(recentChanges.map(change => change.stat))
        setAnimatingStats(newAnimatingStats)

        // Clear animations after 2 seconds
        const timeout = setTimeout(() => {
          setAnimatingStats(new Set())
        }, 2000)

        return () => clearTimeout(timeout)
      }
    }
  }, [card.statChanges])

  // Get stat color based on recent changes - optimized for white text on artwork
  const getStatColor = (stat: 'top' | 'right' | 'bottom' | 'left') => {
    if (!card.statChanges || !animatingStats.has(stat)) return 'text-white'

    const recentChange = card.statChanges
      .filter(change => change.stat === stat && Date.now() - change.timestamp < 2000)
      .sort((a, b) => b.timestamp - a.timestamp)[0]

    if (recentChange) {
      return recentChange.type === 'buff' ? 'text-green-300 font-bold animate-pulse' : 'text-red-300 font-bold animate-pulse'
    }

    return 'text-white'
  }

  return (
    <div className={`relative ${cardClasses} rounded-lg overflow-hidden card transition-all duration-300 shadow-lg ${card.isFlipping ? 'animate-flip' : ''}`}>
      {/* Full card artwork background */}
      <Image
        src={getCardArtwork(card)}
        alt={card.name}
        fill
        sizes={isSmall ? "(max-width: 768px) 96px, 96px" : "(max-width: 768px) 128px, 128px"}
        className="object-cover"
      />

      {/* Stats overlay */}
      {/* Top stat */}
      <div className={`absolute ${isSmall ? 'top-1' : 'top-2'} left-1/2 transform -translate-x-1/2 font-bold ${statTextSize} text-white drop-shadow-lg transition-colors duration-500 ${getStatColor('top')}`}>
        {card.top}
      </div>

      {/* Left stat */}
      <div className={`absolute ${isSmall ? 'left-1' : 'left-2'} top-1/2 transform -translate-y-1/2 font-bold ${statTextSize} text-white drop-shadow-lg transition-colors duration-500 ${getStatColor('left')}`}>
        {card.left}
      </div>

      {/* Right stat */}
      <div className={`absolute ${isSmall ? 'right-1' : 'right-2'} top-1/2 transform -translate-y-1/2 font-bold ${statTextSize} text-white drop-shadow-lg transition-colors duration-500 ${getStatColor('right')}`}>
        {card.right}
      </div>

      {/* Bottom stat */}
      <div className={`absolute ${isSmall ? 'bottom-1' : 'bottom-2'} left-1/2 transform -translate-x-1/2 font-bold ${statTextSize} text-white drop-shadow-lg transition-colors duration-500 ${getStatColor('bottom')}`}>
        {card.bottom}
      </div>

      {/* Shield indicator */}
      {card.shielded && (
        <div className={`absolute top-1 right-1 ${indicatorSize} bg-yellow-400/90 rounded-full border-2 border-yellow-600 flex items-center justify-center animate-pulse backdrop-blur-sm`}>
          🛡️
        </div>
      )}

      {/* Ability indicator */}
      {canUseAbility && (
        <button
          onClick={onAbilityClick}
          className={`absolute top-1 left-1 ${indicatorSize} bg-purple-400/90 rounded-full border-2 border-purple-600 flex items-center justify-center hover:bg-purple-500 transition backdrop-blur-sm`}
          title={card.ability.name}
        >
          ✨
        </button>
      )}

      {/* Used ability indicator */}
      {card.ability.used && (
        <div className={`absolute top-1 left-1 ${indicatorSize} bg-gray-400/90 rounded-full border-2 border-gray-600 flex items-center justify-center backdrop-blur-sm`}>
          ✓
        </div>
      )}

      {/* Owner indicator overlay */}
      {card.owner && (
        <div className={`absolute inset-0 pointer-events-none ${
          card.owner === 'player' ? 'ring-2 ring-blue-400' : 'ring-2 ring-red-400'
        }`} />
      )}
    </div>
  )
}
