import { DuckCard } from '@/lib/types'
import { useEffect, useState } from 'react'

type Props = {
  card: DuckCard
  showAbilityIndicator?: boolean
  onAbilityClick?: () => void
}

export default function Card({ card, showAbilityIndicator = false, onAbilityClick }: Props) {
  const [animatingStats, setAnimatingStats] = useState<Set<string>>(new Set())

  const ownerColor =
    card.owner === 'player' ? 'bg-blue-100 border-blue-400' :
    card.owner === 'enemy' ? 'bg-red-100 border-red-400' :
    'bg-white border-gray-300'

  // All abilities are now onPlay, so no manual ability buttons needed
  const canUseAbility = false

  // Handle stat change animations
  useEffect(() => {
    if (card.statChanges && card.statChanges.length > 0) {
      const recentChanges = card.statChanges.filter(change =>
        Date.now() - change.timestamp < 2000 // Show for 2 seconds
      )

      if (recentChanges.length > 0) {
        const newAnimatingStats = new Set(recentChanges.map(change => change.stat))
        setAnimatingStats(newAnimatingStats)

        // Clear animations after 2 seconds
        const timeout = setTimeout(() => {
          setAnimatingStats(new Set())
        }, 2000)

        return () => clearTimeout(timeout)
      }
    }
  }, [card.statChanges])

  // Get stat color based on recent changes
  const getStatColor = (stat: 'top' | 'right' | 'bottom' | 'left') => {
    if (!card.statChanges || !animatingStats.has(stat)) return 'text-black'

    const recentChange = card.statChanges
      .filter(change => change.stat === stat && Date.now() - change.timestamp < 2000)
      .sort((a, b) => b.timestamp - a.timestamp)[0]

    if (recentChange) {
      return recentChange.type === 'buff' ? 'text-green-600 font-bold animate-pulse' : 'text-red-600 font-bold animate-pulse'
    }

    return 'text-black'
  }

  return (
    <div className={`relative w-20 h-20 rounded-md border p-1 flex flex-col items-center justify-center text-xs ${ownerColor} card transition-all duration-300 ${card.isFlipping ? 'animate-flip' : ''}`}>
      <div className={`absolute top-1 left-1 font-bold transition-colors duration-500 ${getStatColor('top')}`}>{card.top}</div>
      <div className={`absolute right-1 top-1 font-bold transition-colors duration-500 ${getStatColor('right')}`}>{card.right}</div>
      <div className={`absolute bottom-1 right-1 font-bold transition-colors duration-500 ${getStatColor('bottom')}`}>{card.bottom}</div>
      <div className={`absolute bottom-1 left-1 font-bold transition-colors duration-500 ${getStatColor('left')}`}>{card.left}</div>

      {/* Shield indicator */}
      {card.shielded && (
        <div className="absolute top-0 right-0 w-3 h-3 bg-yellow-400 rounded-full border border-yellow-600 text-[8px] flex items-center justify-center animate-pulse">
          🛡️
        </div>
      )}

      {/* Ability indicator */}
      {canUseAbility && (
        <button
          onClick={onAbilityClick}
          className="absolute top-0 left-0 w-3 h-3 bg-purple-400 rounded-full border border-purple-600 text-[8px] flex items-center justify-center hover:bg-purple-500 transition"
          title={card.ability.name}
        >
          ✨
        </button>
      )}

      {/* Used ability indicator */}
      {card.ability.used && (
        <div className="absolute top-0 left-0 w-3 h-3 bg-gray-400 rounded-full border border-gray-600 text-[8px] flex items-center justify-center">
          ✓
        </div>
      )}

      <div className="text-center mt-5">
        <p className="font-semibold text-[10px] leading-tight">{card.name}</p>
        <p className="text-[9px] italic">{card.class}</p>
      </div>
    </div>
  )
}
