import { DuckCard } from '@/lib/types'
import { useEffect, useState } from 'react'
import Image from 'next/image'

type Props = {
  card: DuckCard
  showAbilityIndicator?: boolean
  onAbilityClick?: () => void
}

// Map card classes to their artwork
const getCardArtwork = (card: DuckCard): string => {
  const artworkMap: Record<string, string> = {
    'wizard': '/assets/cardArt/quizardv1.jpg',
    'paladin': '/assets/cardArt/quackadinv1.jpg',
    'rogue': '/assets/cardArt/quoguev1.jpg',
    'cleric': '/assets/cardArt/quickricv1.jpg',
    'ranger': '/assets/cardArt/quangerv1.jpg'
  }

  return artworkMap[card.class] || '/assets/cardArt/quizardv1.jpg'
}

export default function Card({ card, showAbilityIndicator = false, onAbilityClick }: Props) {
  const [animatingStats, setAnimatingStats] = useState<Set<string>>(new Set())

  const ownerColor =
    card.owner === 'player' ? 'bg-blue-100 border-blue-400' :
    card.owner === 'enemy' ? 'bg-red-100 border-red-400' :
    'bg-white border-gray-300'

  // All abilities are now onPlay, so no manual ability buttons needed
  const canUseAbility = false

  // Handle stat change animations
  useEffect(() => {
    if (card.statChanges && card.statChanges.length > 0) {
      const recentChanges = card.statChanges.filter(change =>
        Date.now() - change.timestamp < 2000 // Show for 2 seconds
      )

      if (recentChanges.length > 0) {
        const newAnimatingStats = new Set(recentChanges.map(change => change.stat))
        setAnimatingStats(newAnimatingStats)

        // Clear animations after 2 seconds
        const timeout = setTimeout(() => {
          setAnimatingStats(new Set())
        }, 2000)

        return () => clearTimeout(timeout)
      }
    }
  }, [card.statChanges])

  // Get stat color based on recent changes
  const getStatColor = (stat: 'top' | 'right' | 'bottom' | 'left') => {
    if (!card.statChanges || !animatingStats.has(stat)) return 'text-black'

    const recentChange = card.statChanges
      .filter(change => change.stat === stat && Date.now() - change.timestamp < 2000)
      .sort((a, b) => b.timestamp - a.timestamp)[0]

    if (recentChange) {
      return recentChange.type === 'buff' ? 'text-green-600 font-bold animate-pulse' : 'text-red-600 font-bold animate-pulse'
    }

    return 'text-black'
  }

  return (
    <div className={`relative w-32 h-44 rounded-lg border-2 p-2 flex flex-col items-center justify-between text-sm ${ownerColor} card transition-all duration-300 shadow-lg ${card.isFlipping ? 'animate-flip' : ''}`}>
      {/* Top stat */}
      <div className={`absolute top-2 left-1/2 transform -translate-x-1/2 font-bold text-lg transition-colors duration-500 ${getStatColor('top')}`}>
        {card.top}
      </div>

      {/* Left stat */}
      <div className={`absolute left-2 top-1/2 transform -translate-y-1/2 font-bold text-lg transition-colors duration-500 ${getStatColor('left')}`}>
        {card.left}
      </div>

      {/* Right stat */}
      <div className={`absolute right-2 top-1/2 transform -translate-y-1/2 font-bold text-lg transition-colors duration-500 ${getStatColor('right')}`}>
        {card.right}
      </div>

      {/* Bottom stat */}
      <div className={`absolute bottom-2 left-1/2 transform -translate-x-1/2 font-bold text-lg transition-colors duration-500 ${getStatColor('bottom')}`}>
        {card.bottom}
      </div>

      {/* Shield indicator */}
      {card.shielded && (
        <div className="absolute top-1 right-1 w-6 h-6 bg-yellow-400 rounded-full border-2 border-yellow-600 text-sm flex items-center justify-center animate-pulse">
          🛡️
        </div>
      )}

      {/* Ability indicator */}
      {canUseAbility && (
        <button
          onClick={onAbilityClick}
          className="absolute top-1 left-1 w-6 h-6 bg-purple-400 rounded-full border-2 border-purple-600 text-sm flex items-center justify-center hover:bg-purple-500 transition"
          title={card.ability.name}
        >
          ✨
        </button>
      )}

      {/* Used ability indicator */}
      {card.ability.used && (
        <div className="absolute top-1 left-1 w-6 h-6 bg-gray-400 rounded-full border-2 border-gray-600 text-sm flex items-center justify-center">
          ✓
        </div>
      )}

      {/* Card artwork */}
      <div className="flex flex-col items-center justify-center flex-1 mt-6 mb-4">
        <div className="w-20 h-16 rounded-md overflow-hidden mb-2 border border-gray-300">
          <Image
            src={getCardArtwork(card)}
            alt={card.name}
            width={80}
            height={64}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="text-center">
          <p className="card-name font-bold text-xs leading-tight mb-1">{card.name}</p>
          <p className="text-[10px] italic text-gray-600 mb-1">{card.class}</p>
          <div className="text-[10px] text-gray-700 px-1">
            <p className="ability-name font-semibold">{card.ability.name}</p>
          </div>
        </div>
      </div>
    </div>
  )
}
