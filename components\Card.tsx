import { DuckCard } from '@/lib/types'

type Props = {
  card: DuckCard
  showAbilityIndicator?: boolean
  onAbilityClick?: () => void
}

export default function Card({ card, showAbilityIndicator = false, onAbilityClick }: Props) {
  const ownerColor =
    card.owner === 'player' ? 'bg-blue-100 border-blue-400' :
    card.owner === 'enemy' ? 'bg-red-100 border-red-400' :
    'bg-white border-gray-300'

  const canUseAbility = showAbilityIndicator &&
    card.ability.trigger === 'manual' &&
    !card.ability.used &&
    card.owner === 'player'

  return (
    <div className={`relative w-20 h-20 rounded-md border p-1 flex flex-col items-center justify-center text-xs ${ownerColor} card`}>
      <div className="absolute top-1 left-1 font-bold">{card.top}</div>
      <div className="absolute right-1 top-1 font-bold">{card.right}</div>
      <div className="absolute bottom-1 right-1 font-bold">{card.bottom}</div>
      <div className="absolute bottom-1 left-1 font-bold">{card.left}</div>

      {/* Shield indicator */}
      {card.shielded && (
        <div className="absolute top-0 right-0 w-3 h-3 bg-yellow-400 rounded-full border border-yellow-600 text-[8px] flex items-center justify-center">
          🛡️
        </div>
      )}

      {/* Ability indicator */}
      {canUseAbility && (
        <button
          onClick={onAbilityClick}
          className="absolute top-0 left-0 w-3 h-3 bg-purple-400 rounded-full border border-purple-600 text-[8px] flex items-center justify-center hover:bg-purple-500 transition"
          title={card.ability.name}
        >
          ✨
        </button>
      )}

      {/* Used ability indicator */}
      {card.ability.used && (
        <div className="absolute top-0 left-0 w-3 h-3 bg-gray-400 rounded-full border border-gray-600 text-[8px] flex items-center justify-center">
          ✓
        </div>
      )}

      <div className="text-center mt-5">
        <p className="font-semibold text-[10px] leading-tight">{card.name}</p>
        <p className="text-[9px] italic">{card.class}</p>
      </div>
    </div>
  )
}
