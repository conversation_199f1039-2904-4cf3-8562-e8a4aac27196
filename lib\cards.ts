import { DuckCard } from './types'

export const duckCards: DuckCard[] = [
  // Player Cards
  {
    id: 'wizard-duck-1',
    name: 'Arcane Duck',
    class: 'wizard',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Spellfire',
      description: 'Flip adjacent enemy cards with lower stats',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: 'player'
  },
  {
    id: 'paladin-duck-1',
    name: 'Holy Duck',
    class: 'paladin',
    top: 2,
    right: 4,
    bottom: 5,
    left: 3,
    ability: {
      name: 'Shield of Honor',
      description: 'Prevents this card from being flipped once',
      trigger: 'manual',
      effect: 'shield'
    },
    owner: 'player'
  },
  {
    id: 'rogue-duck-1',
    name: '<PERSON> Duck',
    class: 'rogue',
    top: 4,
    right: 5,
    bottom: 2,
    left: 3,
    ability: {
      name: 'Backstab',
      description: 'Flip adjacent enemy if your stat > theirs',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: 'player'
  },
  {
    id: 'wizard-duck-2',
    name: '<PERSON> Duck',
    class: 'wizard',
    top: 3,
    right: 5,
    bottom: 3,
    left: 4,
    ability: {
      name: '<PERSON> Bolt',
      description: 'Boost all adjacent friendly cards by +1',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: 'player'
  },
  {
    id: 'paladin-duck-2',
    name: 'Guardian Duck',
    class: 'paladin',
    top: 4,
    right: 2,
    bottom: 4,
    left: 5,
    ability: {
      name: 'Divine Protection',
      description: 'Steal an adjacent enemy card',
      trigger: 'manual',
      effect: 'steal'
    },
    owner: 'player'
  },

  // Enemy Cards
  {
    id: 'enemy-wizard-1',
    name: 'Dark Mage Duck',
    class: 'wizard',
    top: 4,
    right: 4,
    bottom: 3,
    left: 3,
    ability: {
      name: 'Dark Magic',
      description: 'Flip adjacent enemy cards with lower stats',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-rogue-1',
    name: 'Assassin Duck',
    class: 'rogue',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Poison Strike',
      description: 'Flip adjacent enemy if your stat > theirs',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-paladin-1',
    name: 'War Duck',
    class: 'paladin',
    top: 3,
    right: 5,
    bottom: 2,
    left: 4,
    ability: {
      name: 'Battle Cry',
      description: 'Boost all adjacent friendly cards by +1',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-wizard-2',
    name: 'Frost Duck',
    class: 'wizard',
    top: 2,
    right: 3,
    bottom: 5,
    left: 4,
    ability: {
      name: 'Ice Shield',
      description: 'Prevents this card from being flipped once',
      trigger: 'manual',
      effect: 'shield'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-rogue-2',
    name: 'Thief Duck',
    class: 'rogue',
    top: 3,
    right: 4,
    bottom: 4,
    left: 3,
    ability: {
      name: 'Steal',
      description: 'Steal an adjacent enemy card',
      trigger: 'manual',
      effect: 'steal'
    },
    owner: 'enemy'
  }
]
