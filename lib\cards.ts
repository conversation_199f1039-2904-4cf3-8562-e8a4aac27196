import { DuckCard } from './types'

export const duckCards: Duck<PERSON><PERSON>[] = [
  {
    id: 'wizard-duck',
    name: '<PERSON> <PERSON>',
    class: 'wizard',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Spellfire',
      description: 'Reduces a random adjacent enemy stat by 1',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: null
  },
  {
    id: 'paladin-duck',
    name: '<PERSON><PERSON><PERSON> <PERSON>',
    class: 'paladin',
    top: 2,
    right: 4,
    bottom: 5,
    left: 3,
    ability: {
      name: 'Shield of Honor',
      description: 'Prevents this card from being flipped once',
      trigger: 'manual',
      effect: 'shield'
    },
    owner: null
  },
  {
    id: 'rogue-duck',
    name: '<PERSON> Duck',
    class: 'rogue',
    top: 4,
    right: 5,
    bottom: 2,
    left: 3,
    ability: {
      name: 'Backstab',
      description: 'Flip adjacent enemy if your stat > theirs',
      trigger: 'onPlay',
      effect: 'flip'
    },
    owner: null
  }
]
