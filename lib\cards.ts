import { DuckCard } from './types'

// All available duck cards for deck building
export const allDuckCards: DuckCard[] = [
  // Wizard Ducks
  {
    id: 'wizard-1',
    name: '🪄 Arcane Quizard',
    class: 'wizard',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Spellfire',
      description: 'Blast an enemy with arcane fire, weakening their mightiest edge.',
      trigger: 'onPlay',
      effect: 'spellfire'
    },
    owner: null
  },
  {
    id: 'wizard-2',
    name: '🪄 Storm Quizard',
    class: 'wizard',
    top: 4,
    right: 4,
    bottom: 3,
    left: 3,
    ability: {
      name: 'Spellfire',
      description: 'Blast an enemy with arcane fire, weakening their mightiest edge.',
      trigger: 'onPlay',
      effect: 'spellfire'
    },
    owner: null
  },
  {
    id: 'wizard-3',
    name: '🪄 Frost Quizard',
    class: 'wizard',
    top: 3,
    right: 5,
    bottom: 2,
    left: 4,
    ability: {
      name: 'Spellfire',
      description: 'Blast an enemy with arcane fire, weakening their mightiest edge.',
      trigger: 'onPlay',
      effect: 'spellfire'
    },
    owner: null
  },

  // Paladin Ducks
  {
    id: 'paladin-1',
    name: '🛡️ Holy Quackadin',
    class: 'paladin',
    top: 2,
    right: 4,
    bottom: 5,
    left: 3,
    ability: {
      name: 'Shield of Honor',
      description: 'Stands firm with divine protection — once.',
      trigger: 'onPlay',
      effect: 'shield'
    },
    owner: null
  },
  {
    id: 'paladin-2',
    name: '🛡️ War Quackadin',
    class: 'paladin',
    top: 3,
    right: 5,
    bottom: 2,
    left: 4,
    ability: {
      name: 'Shield of Honor',
      description: 'Stands firm with divine protection — once.',
      trigger: 'onPlay',
      effect: 'shield'
    },
    owner: null
  },
  {
    id: 'paladin-3',
    name: '🛡️ Guardian Quackadin',
    class: 'paladin',
    top: 4,
    right: 2,
    bottom: 4,
    left: 5,
    ability: {
      name: 'Shield of Honor',
      description: 'Stands firm with divine protection — once.',
      trigger: 'onPlay',
      effect: 'shield'
    },
    owner: null
  },

  // Rogue Ducks
  {
    id: 'rogue-1',
    name: '🗡️ Shadow Quogue',
    class: 'rogue',
    top: 4,
    right: 5,
    bottom: 2,
    left: 3,
    ability: {
      name: 'Backstab',
      description: 'A swift strike from the shadows — if it\'s better, it flips.',
      trigger: 'onPlay',
      effect: 'backstab'
    },
    owner: null
  },
  {
    id: 'rogue-2',
    name: '🗡️ Assassin Quogue',
    class: 'rogue',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Backstab',
      description: 'A swift strike from the shadows — if it\'s better, it flips.',
      trigger: 'onPlay',
      effect: 'backstab'
    },
    owner: null
  },
  {
    id: 'rogue-3',
    name: '🗡️ Thief Quogue',
    class: 'rogue',
    top: 3,
    right: 4,
    bottom: 4,
    left: 3,
    ability: {
      name: 'Backstab',
      description: 'A swift strike from the shadows — if it\'s better, it flips.',
      trigger: 'onPlay',
      effect: 'backstab'
    },
    owner: null
  },

  // Cleric Ducks
  {
    id: 'cleric-1',
    name: '🧠 Divine Quickric',
    class: 'cleric',
    top: 3,
    right: 2,
    bottom: 4,
    left: 5,
    ability: {
      name: 'Blessing of Feathers',
      description: 'Offers divine buffs to bolster nearby allies.',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: null
  },
  {
    id: 'cleric-2',
    name: '🧠 Healing Quickric',
    class: 'cleric',
    top: 2,
    right: 3,
    bottom: 5,
    left: 4,
    ability: {
      name: 'Blessing of Feathers',
      description: 'Offers divine buffs to bolster nearby allies.',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: null
  },
  {
    id: 'cleric-3',
    name: '🧠 Sage Quickric',
    class: 'cleric',
    top: 4,
    right: 3,
    bottom: 3,
    left: 4,
    ability: {
      name: 'Blessing of Feathers',
      description: 'Offers divine buffs to bolster nearby allies.',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: null
  },

  // Ranger Ducks
  {
    id: 'ranger-1',
    name: '🏹 Forest Quanger',
    class: 'ranger',
    top: 4,
    right: 4,
    bottom: 3,
    left: 3,
    ability: {
      name: 'Volley Shot',
      description: 'Unleashes a flurry of arrows to wear down nearby foes.',
      trigger: 'onPlay',
      effect: 'volley'
    },
    owner: null
  },
  {
    id: 'ranger-2',
    name: '🏹 Hunter Quanger',
    class: 'ranger',
    top: 3,
    right: 4,
    bottom: 4,
    left: 3,
    ability: {
      name: 'Volley Shot',
      description: 'Unleashes a flurry of arrows to wear down nearby foes.',
      trigger: 'onPlay',
      effect: 'volley'
    },
    owner: null
  },
  {
    id: 'ranger-3',
    name: '🏹 Scout Quanger',
    class: 'ranger',
    top: 5,
    right: 2,
    bottom: 3,
    left: 4,
    ability: {
      name: 'Volley Shot',
      description: 'Unleashes a flurry of arrows to wear down nearby foes.',
      trigger: 'onPlay',
      effect: 'volley'
    },
    owner: null
  }
]

// Default deck configurations
export const defaultPlayerDeck: DuckCard[] = [
  { ...allDuckCards[0], owner: 'player' }, // Arcane Quizard
  { ...allDuckCards[3], owner: 'player' }, // Holy Quackadin
  { ...allDuckCards[6], owner: 'player' }, // Shadow Quogue
  { ...allDuckCards[9], owner: 'player' }, // Divine Quickric
  { ...allDuckCards[12], owner: 'player' } // Forest Quanger
]

export const defaultEnemyDeck: DuckCard[] = [
  { ...allDuckCards[1], owner: 'enemy' }, // Storm Quizard
  { ...allDuckCards[4], owner: 'enemy' }, // War Quackadin
  { ...allDuckCards[7], owner: 'enemy' }, // Assassin Quogue
  { ...allDuckCards[10], owner: 'enemy' }, // Healing Quickric
  { ...allDuckCards[13], owner: 'enemy' } // Hunter Quanger
]

// Legacy export for backward compatibility
export const duckCards: DuckCard[] = [...defaultPlayerDeck, ...defaultEnemyDeck]
