import { DuckCard } from './types'

export const duckCards: DuckCard[] = [
  // Player Cards
  {
    id: 'player-wizard-1',
    name: '🪄 Wizard Duck',
    class: 'wizard',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Spellfire',
      description: 'Blast an enemy with arcane fire, weakening their mightiest edge.',
      trigger: 'onPlay',
      effect: 'spellfire'
    },
    owner: 'player'
  },
  {
    id: 'player-paladin-1',
    name: '🛡️ Paladin Duck',
    class: 'paladin',
    top: 2,
    right: 4,
    bottom: 5,
    left: 3,
    ability: {
      name: 'Shield of Honor',
      description: 'Stands firm with divine protection — once.',
      trigger: 'onPlay',
      effect: 'shield'
    },
    owner: 'player'
  },
  {
    id: 'player-rogue-1',
    name: '🗡️ Rogue Duck',
    class: 'rogue',
    top: 4,
    right: 5,
    bottom: 2,
    left: 3,
    ability: {
      name: 'Backstab',
      description: 'A swift strike from the shadows — if it\'s better, it flips.',
      trigger: 'onPlay',
      effect: 'backstab'
    },
    owner: 'player'
  },
  {
    id: 'player-cleric-1',
    name: '🧠 Cleric Duck',
    class: 'cleric',
    top: 3,
    right: 2,
    bottom: 4,
    left: 5,
    ability: {
      name: 'B<PERSON><PERSON> of Feathers',
      description: 'Offers divine buffs to bolster nearby allies.',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: 'player'
  },
  {
    id: 'player-ranger-1',
    name: '🏹 Ranger Duck',
    class: 'ranger',
    top: 4,
    right: 4,
    bottom: 3,
    left: 3,
    ability: {
      name: 'Volley Shot',
      description: 'Unleashes a flurry of arrows to wear down nearby foes.',
      trigger: 'onPlay',
      effect: 'volley'
    },
    owner: 'player'
  },

  // Enemy Cards
  {
    id: 'enemy-wizard-1',
    name: '🪄 Dark Wizard',
    class: 'wizard',
    top: 4,
    right: 4,
    bottom: 3,
    left: 3,
    ability: {
      name: 'Spellfire',
      description: 'Blast an enemy with arcane fire, weakening their mightiest edge.',
      trigger: 'onPlay',
      effect: 'spellfire'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-paladin-1',
    name: '🛡️ War Paladin',
    class: 'paladin',
    top: 3,
    right: 5,
    bottom: 2,
    left: 4,
    ability: {
      name: 'Shield of Honor',
      description: 'Stands firm with divine protection — once.',
      trigger: 'onPlay',
      effect: 'shield'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-rogue-1',
    name: '🗡️ Shadow Rogue',
    class: 'rogue',
    top: 5,
    right: 3,
    bottom: 4,
    left: 2,
    ability: {
      name: 'Backstab',
      description: 'A swift strike from the shadows — if it\'s better, it flips.',
      trigger: 'onPlay',
      effect: 'backstab'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-cleric-1',
    name: '🧠 Dark Cleric',
    class: 'cleric',
    top: 2,
    right: 3,
    bottom: 5,
    left: 4,
    ability: {
      name: 'Blessing of Feathers',
      description: 'Offers divine buffs to bolster nearby allies.',
      trigger: 'onPlay',
      effect: 'buff'
    },
    owner: 'enemy'
  },
  {
    id: 'enemy-ranger-1',
    name: '🏹 Hunter Ranger',
    class: 'ranger',
    top: 3,
    right: 4,
    bottom: 4,
    left: 3,
    ability: {
      name: 'Volley Shot',
      description: 'Unleashes a flurry of arrows to wear down nearby foes.',
      trigger: 'onPlay',
      effect: 'volley'
    },
    owner: 'enemy'
  }
]
