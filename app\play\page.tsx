'use client'

import { useState, useEffect } from 'react'
import { duckCards } from '@/lib/cards'
import { DuckCard } from '@/lib/types'
import Board from '@/components/Board'
import Card from '@/components/Card'

export default function PlayPage() {
  const [board, setBoard] = useState<(DuckCard | null)[]>(Array(9).fill(null))
  const [hand, setHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'player').slice(0, 5))
  const [selectedCard, setSelectedCard] = useState<DuckCard | null>(null)
  const [currentTurn, setCurrentTurn] = useState<'player' | 'enemy'>('player')
  const [enemyHand, setEnemyHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'enemy').slice(0, 5))


const handleTileClick = (index: number) => {
    if (currentTurn !== 'player') return
    if (!selectedCard || board[index]) return

    const newBoard = [...board]
    newBoard[index] = { ...selectedCard, owner: 'player' }
    setBoard(newBoard)
    setHand(hand.filter((card) => card.id !== selectedCard.id))
    setSelectedCard(null)
    setCurrentTurn('enemy')
}

useEffect(() => {
  if (currentTurn === 'enemy') {
    const timeout = setTimeout(() => {
      const availableTiles = board
        .map((tile, index) => (tile === null ? index : null))
        .filter((i): i is number => i !== null)

      if (availableTiles.length === 0 || enemyHand.length === 0) return

      const randomIndex = availableTiles[Math.floor(Math.random() * availableTiles.length)]
      const randomCard = enemyHand[Math.floor(Math.random() * enemyHand.length)]

      const newBoard = [...board]
      newBoard[randomIndex] = { ...randomCard, owner: 'enemy' }
      setBoard(newBoard)
      setEnemyHand(enemyHand.filter((c) => c.id !== randomCard.id))
      setCurrentTurn('player')
    }, 1000)

    return () => clearTimeout(timeout)
  }
}, [currentTurn, board, enemyHand])


  return (
    <main className="p-8 space-y-6 text-center">
      <h1 className="text-2xl font-bold">🦆 Duck Dominion</h1>

      <Board board={board} onTileClick={handleTileClick} />

      <div className="mt-6">
        <h2 className="font-semibold mb-2">Your Hand</h2>
        <div className="flex justify-center gap-3">
          {hand.map((card) => (
            <button
              key={card.id}
              onClick={() => setSelectedCard(card)}
              className={`p-2 border rounded-md ${
                selectedCard?.id === card.id ? 'border-yellow-400' : 'border-transparent'
              }`}
            >
              <Card card={card} />
            </button>
          ))}
        </div>
      </div>
    </main>
  )
}
