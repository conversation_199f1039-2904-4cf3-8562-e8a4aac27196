'use client'

import { useState, useEffect } from 'react'
import { duckCards } from '@/lib/cards'
import { DuckCard } from '@/lib/types'
import {
  executeCardFlipWithAbilities,
  calculateScores,
  checkGameEnd,
  findBestAIMoveAdvanced,
  clearFlippingAnimations
} from '@/lib/gameLogic'
import Board from '@/components/Board'
import Card from '@/components/Card'
import HelpModal, { HelpButton } from '@/components/HelpModal'

export default function PlayPage() {
  const [board, setBoard] = useState<(DuckCard | null)[]>(Array(9).fill(null))
  const [hand, setHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'player').slice(0, 5))
  const [selectedCard, setSelectedCard] = useState<DuckCard | null>(null)
  const [currentTurn, setCurrentTurn] = useState<'player' | 'enemy'>('player')
  const [enemyHand, setEnemyHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'enemy').slice(0, 5))
  const [gameStatus, setGameStatus] = useState<'playing' | 'playerWin' | 'enemyWin' | 'draw'>('playing')
  const [playerScore, setPlayerScore] = useState(0)
  const [enemyScore, setEnemyScore] = useState(0)
  const [showHelp, setShowHelp] = useState(false)


const handleTileClick = (index: number) => {
    if (currentTurn !== 'player' || gameStatus !== 'playing') return
    if (!selectedCard || board[index]) return

    // Place card and execute flipping logic with abilities
    const newBoard = [...board]
    newBoard[index] = { ...selectedCard, owner: 'player' }
    const boardAfterFlip = executeCardFlipWithAbilities(newBoard, index, { ...selectedCard, owner: 'player' })

    setBoard(boardAfterFlip)
    setHand(hand.filter((card) => card.id !== selectedCard.id))
    setSelectedCard(null)

    // Clear flipping animations after a delay
    setTimeout(() => {
      setBoard(prevBoard => clearFlippingAnimations(prevBoard))
    }, 600)

    // Update scores
    const scores = calculateScores(boardAfterFlip)
    setPlayerScore(scores.playerScore)
    setEnemyScore(scores.enemyScore)

    // Check for game end
    const newGameStatus = checkGameEnd(boardAfterFlip, hand.filter((card) => card.id !== selectedCard.id), enemyHand)
    setGameStatus(newGameStatus)

    if (newGameStatus === 'playing') {
      setCurrentTurn('enemy')
    }
}

useEffect(() => {
  if (currentTurn === 'enemy' && gameStatus === 'playing') {
    const timeout = setTimeout(() => {
      if (enemyHand.length === 0) return

      // Use advanced AI to find the best move
      const bestMove = findBestAIMoveAdvanced(board, enemyHand)

      if (!bestMove) return

      const selectedCard = enemyHand[bestMove.cardIndex]

      // Place card and execute flipping logic with abilities
      const newBoard = [...board]
      newBoard[bestMove.boardPosition] = { ...selectedCard, owner: 'enemy' }
      const boardAfterFlip = executeCardFlipWithAbilities(newBoard, bestMove.boardPosition, { ...selectedCard, owner: 'enemy' })

      setBoard(boardAfterFlip)
      setEnemyHand(enemyHand.filter((c) => c.id !== selectedCard.id))

      // Clear flipping animations after a delay
      setTimeout(() => {
        setBoard(prevBoard => clearFlippingAnimations(prevBoard))
      }, 600)

      // Update scores
      const scores = calculateScores(boardAfterFlip)
      setPlayerScore(scores.playerScore)
      setEnemyScore(scores.enemyScore)

      // Check for game end
      const newGameStatus = checkGameEnd(boardAfterFlip, hand, enemyHand.filter((c) => c.id !== selectedCard.id))
      setGameStatus(newGameStatus)

      if (newGameStatus === 'playing') {
        setCurrentTurn('player')
      }
    }, 1500) // Slightly longer delay to show AI "thinking"

    return () => clearTimeout(timeout)
  }
}, [currentTurn, board, enemyHand, hand, gameStatus])


  const restartGame = () => {
    setBoard(Array(9).fill(null))
    setHand(duckCards.filter(card => card.owner === 'player').slice(0, 5))
    setEnemyHand(duckCards.filter(card => card.owner === 'enemy').slice(0, 5))
    setSelectedCard(null)
    setCurrentTurn('player')
    setGameStatus('playing')
    setPlayerScore(0)
    setEnemyScore(0)
  }

  // No longer needed since all abilities are onPlay
  const handleAbilityClick = (_cardIndex: number) => {
    // All abilities are now automatic onPlay, so this does nothing
    return
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-green-50 flex flex-col">
      {/* Header */}
      <header className="p-4 text-center">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">🦆 Duck Dominion</h1>

        {/* Score Display */}
        <div className="flex justify-center gap-8 text-xl font-semibold mb-2">
          <div className="text-blue-600 bg-blue-100 px-4 py-2 rounded-lg">Player: {playerScore}</div>
          <div className="text-red-600 bg-red-100 px-4 py-2 rounded-lg">Enemy: {enemyScore}</div>
        </div>

        {/* Turn Indicator */}
        <div className="text-lg font-medium text-gray-700">
          {gameStatus === 'playing' ? (
            currentTurn === 'player' ? "🎯 Your Turn" : "🤖 Enemy Turn"
          ) : null}
        </div>
      </header>

      {/* Main Game Area */}
      <main className="flex-1 flex items-center justify-center p-4">
        <Board board={board} onTileClick={handleTileClick} onAbilityClick={handleAbilityClick} />
      </main>

      {/* Game End Modal */}
      {gameStatus !== 'playing' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-xl text-center space-y-4 shadow-2xl">
            <h2 className="text-4xl font-bold">
              {gameStatus === 'playerWin' && '🎉 You Win!'}
              {gameStatus === 'enemyWin' && '😢 You Lose!'}
              {gameStatus === 'draw' && '🤝 It\'s a Draw!'}
            </h2>
            <p className="text-xl">
              Final Score: Player {playerScore} - {enemyScore} Enemy
            </p>
            <button
              onClick={restartGame}
              className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition text-lg font-semibold"
            >
              Play Again
            </button>
          </div>
        </div>
      )}

      {/* Player Hand */}
      <div className="fixed bottom-0 left-0 right-0 p-4">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-center font-semibold mb-4 text-gray-700">Your Hand ({hand.length} cards)</h2>
          <div className="flex justify-center items-end" style={{ height: '200px' }}>
            {hand.map((card, index) => {
              const totalCards = hand.length
              const centerIndex = (totalCards - 1) / 2
              const angleOffset = (index - centerIndex) * 8 // 8 degrees per card
              const yOffset = Math.abs(index - centerIndex) * 10 // Slight arc effect
              const xOffset = (index - centerIndex) * 20 // Spread cards horizontally

              return (
                <button
                  key={card.id}
                  onClick={() => setSelectedCard(card)}
                  disabled={currentTurn !== 'player' || gameStatus !== 'playing'}
                  className={`absolute transition-all duration-300 hover:scale-110 hover:-translate-y-8 ${
                    selectedCard?.id === card.id
                      ? 'scale-110 -translate-y-8 ring-4 ring-yellow-400 ring-opacity-75'
                      : ''
                  } ${
                    currentTurn !== 'player' || gameStatus !== 'playing'
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:z-10'
                  }`}
                  style={{
                    transform: `translateX(${xOffset}px) translateY(${yOffset}px) rotate(${angleOffset}deg)`,
                    zIndex: selectedCard?.id === card.id ? 20 : 10 - Math.abs(index - centerIndex)
                  }}
                >
                  <Card card={card} />
                </button>
              )
            })}
          </div>

          {selectedCard && (
            <div className="mt-6 p-4 bg-white/90 backdrop-blur rounded-lg max-w-md mx-auto shadow-lg">
              <h3 className="font-bold text-lg">{selectedCard.name}</h3>
              <p className="text-sm text-gray-600 mt-1">{selectedCard.ability.description}</p>
              <p className="text-xs text-gray-500 mt-2">
                Stats: {selectedCard.top}-{selectedCard.right}-{selectedCard.bottom}-{selectedCard.left} (T-R-B-L)
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Help Button and Modal */}
      <HelpButton onClick={() => setShowHelp(true)} />
      <HelpModal isOpen={showHelp} onClose={() => setShowHelp(false)} />
    </div>
  )
}
