'use client'

import { useState, useEffect } from 'react'
import { duckCards } from '@/lib/cards'
import { DuckCard } from '@/lib/types'
import {
  executeCardFlipWithAbilities,
  calculateScores,
  checkGameEnd,
  findBestAIMoveAdvanced
} from '@/lib/gameLogic'
import Board from '@/components/Board'
import Card from '@/components/Card'

export default function PlayPage() {
  const [board, setBoard] = useState<(DuckCard | null)[]>(Array(9).fill(null))
  const [hand, setHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'player').slice(0, 5))
  const [selectedCard, setSelectedCard] = useState<DuckCard | null>(null)
  const [currentTurn, setCurrentTurn] = useState<'player' | 'enemy'>('player')
  const [enemyHand, setEnemyHand] = useState<DuckCard[]>(duckCards.filter(card => card.owner === 'enemy').slice(0, 5))
  const [gameStatus, setGameStatus] = useState<'playing' | 'playerWin' | 'enemyWin' | 'draw'>('playing')
  const [playerScore, setPlayerScore] = useState(0)
  const [enemyScore, setEnemyScore] = useState(0)


const handleTileClick = (index: number) => {
    if (currentTurn !== 'player' || gameStatus !== 'playing') return
    if (!selectedCard || board[index]) return

    // Place card and execute flipping logic with abilities
    const newBoard = [...board]
    newBoard[index] = { ...selectedCard, owner: 'player' }
    const boardAfterFlip = executeCardFlipWithAbilities(newBoard, index, { ...selectedCard, owner: 'player' })

    setBoard(boardAfterFlip)
    setHand(hand.filter((card) => card.id !== selectedCard.id))
    setSelectedCard(null)

    // Update scores
    const scores = calculateScores(boardAfterFlip)
    setPlayerScore(scores.playerScore)
    setEnemyScore(scores.enemyScore)

    // Check for game end
    const newGameStatus = checkGameEnd(boardAfterFlip, hand.filter((card) => card.id !== selectedCard.id), enemyHand)
    setGameStatus(newGameStatus)

    if (newGameStatus === 'playing') {
      setCurrentTurn('enemy')
    }
}

useEffect(() => {
  if (currentTurn === 'enemy' && gameStatus === 'playing') {
    const timeout = setTimeout(() => {
      if (enemyHand.length === 0) return

      // Use advanced AI to find the best move
      const bestMove = findBestAIMoveAdvanced(board, enemyHand)

      if (!bestMove) return

      const selectedCard = enemyHand[bestMove.cardIndex]

      // Place card and execute flipping logic with abilities
      const newBoard = [...board]
      newBoard[bestMove.boardPosition] = { ...selectedCard, owner: 'enemy' }
      const boardAfterFlip = executeCardFlipWithAbilities(newBoard, bestMove.boardPosition, { ...selectedCard, owner: 'enemy' })

      setBoard(boardAfterFlip)
      setEnemyHand(enemyHand.filter((c) => c.id !== selectedCard.id))

      // Update scores
      const scores = calculateScores(boardAfterFlip)
      setPlayerScore(scores.playerScore)
      setEnemyScore(scores.enemyScore)

      // Check for game end
      const newGameStatus = checkGameEnd(boardAfterFlip, hand, enemyHand.filter((c) => c.id !== selectedCard.id))
      setGameStatus(newGameStatus)

      if (newGameStatus === 'playing') {
        setCurrentTurn('player')
      }
    }, 1500) // Slightly longer delay to show AI "thinking"

    return () => clearTimeout(timeout)
  }
}, [currentTurn, board, enemyHand, hand, gameStatus])


  const restartGame = () => {
    setBoard(Array(9).fill(null))
    setHand(duckCards.filter(card => card.owner === 'player').slice(0, 5))
    setEnemyHand(duckCards.filter(card => card.owner === 'enemy').slice(0, 5))
    setSelectedCard(null)
    setCurrentTurn('player')
    setGameStatus('playing')
    setPlayerScore(0)
    setEnemyScore(0)
  }

  // No longer needed since all abilities are onPlay
  const handleAbilityClick = (_cardIndex: number) => {
    // All abilities are now automatic onPlay, so this does nothing
    return
  }

  return (
    <main className="p-8 space-y-6 text-center">
      <h1 className="text-2xl font-bold">🦆 Duck Dominion</h1>

      {/* Score Display */}
      <div className="flex justify-center gap-8 text-lg font-semibold">
        <div className="text-blue-600">Player: {playerScore}</div>
        <div className="text-red-600">Enemy: {enemyScore}</div>
      </div>

      {/* Turn Indicator */}
      <div className="text-sm text-gray-600">
        {gameStatus === 'playing' ? (
          currentTurn === 'player' ? "Your Turn" : "Enemy Turn"
        ) : null}
      </div>

      <Board board={board} onTileClick={handleTileClick} onAbilityClick={handleAbilityClick} />

      {/* Game End Modal */}
      {gameStatus !== 'playing' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-8 rounded-lg text-center space-y-4">
            <h2 className="text-3xl font-bold">
              {gameStatus === 'playerWin' && '🎉 You Win!'}
              {gameStatus === 'enemyWin' && '😢 You Lose!'}
              {gameStatus === 'draw' && '🤝 It\'s a Draw!'}
            </h2>
            <p className="text-lg">
              Final Score: Player {playerScore} - {enemyScore} Enemy
            </p>
            <button
              onClick={restartGame}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
            >
              Play Again
            </button>
          </div>
        </div>
      )}

      <div className="mt-6">
        <h2 className="font-semibold mb-2">Your Hand ({hand.length} cards)</h2>
        <div className="flex justify-center gap-3">
          {hand.map((card) => (
            <button
              key={card.id}
              onClick={() => setSelectedCard(card)}
              disabled={currentTurn !== 'player' || gameStatus !== 'playing'}
              className={`p-2 border rounded-md transition ${
                selectedCard?.id === card.id ? 'border-yellow-400 bg-yellow-50' : 'border-transparent'
              } ${
                currentTurn !== 'player' || gameStatus !== 'playing'
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:border-gray-300'
              }`}
            >
              <Card card={card} />
            </button>
          ))}
        </div>
        {selectedCard && (
          <div className="mt-4 p-3 bg-gray-100 rounded-lg max-w-md mx-auto">
            <h3 className="font-semibold">{selectedCard.name}</h3>
            <p className="text-sm text-gray-600 mt-1">{selectedCard.ability.description}</p>
            <p className="text-xs text-gray-500 mt-1">
              Stats: {selectedCard.top}-{selectedCard.right}-{selectedCard.bottom}-{selectedCard.left} (T-R-B-L)
            </p>
          </div>
        )}
      </div>

      {/* Game Instructions */}
      <div className="mt-8 max-w-2xl mx-auto text-sm text-gray-600 bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">How to Play:</h3>
        <ul className="space-y-1 text-left">
          <li>• Select a card from your hand and place it on the board</li>
          <li>• Cards flip adjacent enemy cards if your stat &gt; their opposing stat</li>
          <li>• ✨ Click purple ability buttons to use manual abilities</li>
          <li>• 🛡️ Yellow shields protect cards from being flipped once</li>
          <li>• Win by controlling more cards when the game ends!</li>
        </ul>
      </div>
    </main>
  )
}
