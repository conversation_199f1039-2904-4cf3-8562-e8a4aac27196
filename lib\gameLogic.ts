import { DuckCard } from './types'

export type GameState = {
  board: (DuckCard | null)[]
  playerHand: DuckCard[]
  enemyHand: DuckCard[]
  currentTurn: 'player' | 'enemy'
  gameStatus: 'playing' | 'playerWin' | 'enemyWin' | 'draw'
  playerScore: number
  enemyScore: number
}

// Get adjacent positions for a board index (3x3 grid)
export function getAdjacentPositions(index: number): number[] {
  const row = Math.floor(index / 3)
  const col = index % 3
  const adjacent: number[] = []

  // Top
  if (row > 0) adjacent.push((row - 1) * 3 + col)
  // Right
  if (col < 2) adjacent.push(row * 3 + (col + 1))
  // Bottom
  if (row < 2) adjacent.push((row + 1) * 3 + col)
  // Left
  if (col > 0) adjacent.push(row * 3 + (col - 1))

  return adjacent
}

// Get the stat value for a specific direction
export function getStatForDirection(card: DuckCard, direction: 'top' | 'right' | 'bottom' | 'left'): number {
  return card[direction]
}

// Get the opposite direction
export function getOppositeDirection(direction: 'top' | 'right' | 'bottom' | 'left'): 'top' | 'right' | 'bottom' | 'left' {
  const opposites = {
    top: 'bottom' as const,
    right: 'left' as const,
    bottom: 'top' as const,
    left: 'right' as const
  }
  return opposites[direction]
}

// Get direction from one position to another
export function getDirection(fromIndex: number, toIndex: number): 'top' | 'right' | 'bottom' | 'left' | null {
  const fromRow = Math.floor(fromIndex / 3)
  const fromCol = fromIndex % 3
  const toRow = Math.floor(toIndex / 3)
  const toCol = toIndex % 3

  if (fromRow === toRow) {
    if (toCol === fromCol + 1) return 'right'
    if (toCol === fromCol - 1) return 'left'
  }
  if (fromCol === toCol) {
    if (toRow === fromRow + 1) return 'bottom'
    if (toRow === fromRow - 1) return 'top'
  }
  return null
}

// Check if a card can flip an adjacent card
export function canFlipCard(attackingCard: DuckCard, defendingCard: DuckCard, direction: 'top' | 'right' | 'bottom' | 'left'): boolean {
  const attackingStat = getStatForDirection(attackingCard, direction)
  const defendingStat = getStatForDirection(defendingCard, getOppositeDirection(direction))
  return attackingStat > defendingStat
}

// Execute card flipping logic when a card is placed
export function executeCardFlip(board: (DuckCard | null)[], placedIndex: number, placedCard: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(placedIndex)

  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    if (!adjacentCard || adjacentCard.owner === placedCard.owner) return

    const direction = getDirection(placedIndex, adjIndex)
    if (!direction) return

    if (canFlipCard(placedCard, adjacentCard, direction)) {
      // Flip the card by changing its owner
      newBoard[adjIndex] = { ...adjacentCard, owner: placedCard.owner }
    }
  })

  return newBoard
}

// Calculate current scores based on board control
export function calculateScores(board: (DuckCard | null)[]): { playerScore: number, enemyScore: number } {
  let playerScore = 0
  let enemyScore = 0

  board.forEach(card => {
    if (card?.owner === 'player') playerScore++
    else if (card?.owner === 'enemy') enemyScore++
  })

  return { playerScore, enemyScore }
}

// Check if the game is over and determine winner
export function checkGameEnd(board: (DuckCard | null)[], playerHand: DuckCard[], enemyHand: DuckCard[]): 'playing' | 'playerWin' | 'enemyWin' | 'draw' {
  const isBoardFull = board.every(cell => cell !== null)
  const noMovesLeft = playerHand.length === 0 && enemyHand.length === 0

  if (!isBoardFull && !noMovesLeft) {
    return 'playing'
  }

  const { playerScore, enemyScore } = calculateScores(board)

  if (playerScore > enemyScore) return 'playerWin'
  if (enemyScore > playerScore) return 'enemyWin'
  return 'draw'
}

// Execute card abilities
export function executeAbility(
  board: (DuckCard | null)[],
  cardIndex: number,
  card: DuckCard
): (DuckCard | null)[] {
  const newBoard = [...board]

  if (card.ability.used) return newBoard

  switch (card.ability.effect) {
    case 'spellfire':
      return executeSpellfireAbility(newBoard, cardIndex, card)
    case 'shield':
      return executeShieldAbility(newBoard, cardIndex, card)
    case 'backstab':
      return executeBackstabAbility(newBoard, cardIndex, card)
    case 'buff':
      return executeBlessingAbility(newBoard, cardIndex, card)
    case 'volley':
      return executeVolleyAbility(newBoard, cardIndex, card)
    // Legacy abilities for backward compatibility
    case 'flip':
      return executeFlipAbility(newBoard, cardIndex, card)
    case 'steal':
      return executeStealAbility(newBoard, cardIndex, card)
    default:
      return newBoard
  }
}

// Flip ability: Flip adjacent enemy cards
function executeFlipAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    if (!adjacentCard || adjacentCard.owner === card.owner) return

    const direction = getDirection(cardIndex, adjIndex)
    if (!direction) return

    if (canFlipCard(card, adjacentCard, direction)) {
      newBoard[adjIndex] = { ...adjacentCard, owner: card.owner }
    }
  })

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Buff ability: Increase stats of adjacent friendly cards
function executeBuffAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    if (!adjacentCard || adjacentCard.owner !== card.owner) return

    // Buff all stats by 1 (max 9)
    newBoard[adjIndex] = {
      ...adjacentCard,
      top: Math.min(9, adjacentCard.top + 1),
      right: Math.min(9, adjacentCard.right + 1),
      bottom: Math.min(9, adjacentCard.bottom + 1),
      left: Math.min(9, adjacentCard.left + 1)
    }
  })

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Shield ability: Prevent card from being flipped once
function executeShieldAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]

  // Add shield property to the card
  newBoard[cardIndex] = {
    ...card,
    ability: { ...card.ability, used: true },
    shielded: true
  }

  return newBoard
}

// Spellfire ability: Reduce the highest stat of one adjacent enemy card by 1
function executeSpellfireAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  // Find adjacent enemy cards
  const enemyCards = adjacentPositions
    .map(adjIndex => ({ card: newBoard[adjIndex], index: adjIndex }))
    .filter(({ card: adjacentCard }) => adjacentCard && adjacentCard.owner !== card.owner)

  if (enemyCards.length > 0) {
    // Pick a random enemy card
    const targetEnemy = enemyCards[Math.floor(Math.random() * enemyCards.length)]
    const targetCard = targetEnemy.card!

    // Find the highest stat
    const stats = [
      { value: targetCard.top, name: 'top' as const },
      { value: targetCard.right, name: 'right' as const },
      { value: targetCard.bottom, name: 'bottom' as const },
      { value: targetCard.left, name: 'left' as const }
    ]
    const highestStat = stats.reduce((max, stat) => stat.value > max.value ? stat : max)

    // Reduce the highest stat by 1 (minimum 1)
    newBoard[targetEnemy.index] = {
      ...targetCard,
      [highestStat.name]: Math.max(1, highestStat.value - 1)
    }
  }

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Backstab ability: Flip one adjacent enemy card regardless of stats
function executeBackstabAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  // Find adjacent enemy cards
  const enemyCards = adjacentPositions
    .map(adjIndex => ({ card: newBoard[adjIndex], index: adjIndex }))
    .filter(({ card: adjacentCard }) => adjacentCard && adjacentCard.owner !== card.owner)

  if (enemyCards.length > 0) {
    // Pick a random enemy card and flip it
    const targetEnemy = enemyCards[Math.floor(Math.random() * enemyCards.length)]
    newBoard[targetEnemy.index] = { ...targetEnemy.card!, owner: card.owner }
  }

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Blessing of Feathers ability: Increase all adjacent friendly stats by +1
function executeBlessingAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    if (!adjacentCard || adjacentCard.owner !== card.owner) return

    // Buff all stats by 1 (max 9)
    newBoard[adjIndex] = {
      ...adjacentCard,
      top: Math.min(9, adjacentCard.top + 1),
      right: Math.min(9, adjacentCard.right + 1),
      bottom: Math.min(9, adjacentCard.bottom + 1),
      left: Math.min(9, adjacentCard.left + 1)
    }
  })

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Volley Shot ability: Deal 1 damage to all adjacent enemy card stats (random stat)
function executeVolleyAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    if (!adjacentCard || adjacentCard.owner === card.owner) return

    // Pick a random stat to reduce
    const stats = ['top', 'right', 'bottom', 'left'] as const
    const randomStat = stats[Math.floor(Math.random() * stats.length)]

    // Reduce the random stat by 1 (minimum 1)
    newBoard[adjIndex] = {
      ...adjacentCard,
      [randomStat]: Math.max(1, adjacentCard[randomStat] - 1)
    }
  })

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Steal ability: Convert an adjacent enemy card (legacy)
function executeStealAbility(board: (DuckCard | null)[], cardIndex: number, card: DuckCard): (DuckCard | null)[] {
  const newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(cardIndex)

  const targetIdx = adjacentPositions.find(adjIndex => {
    const adjacentCard = newBoard[adjIndex]
    return adjacentCard && adjacentCard.owner !== card.owner
  })

  if (targetIdx !== undefined) {
    const targetCard = newBoard[targetIdx]
    if (targetCard && targetCard.owner !== card.owner) {
      newBoard[targetIdx] = { ...targetCard, owner: card.owner }
    }
  }

  // Mark ability as used
  newBoard[cardIndex] = { ...card, ability: { ...card.ability, used: true } }
  return newBoard
}

// Check if a card can be flipped (considering shields)
export function canFlipCardWithShield(attackingCard: DuckCard, defendingCard: DuckCard, direction: 'top' | 'right' | 'bottom' | 'left'): boolean {
  if (defendingCard.shielded) return false
  return canFlipCard(attackingCard, defendingCard, direction)
}

// Enhanced card flip execution that considers abilities and shields
export function executeCardFlipWithAbilities(board: (DuckCard | null)[], placedIndex: number, placedCard: DuckCard): (DuckCard | null)[] {
  let newBoard = [...board]
  const adjacentPositions = getAdjacentPositions(placedIndex)

  // First, execute normal flipping (only for non-backstab abilities)
  if (placedCard.ability.effect !== 'backstab') {
    adjacentPositions.forEach(adjIndex => {
      const adjacentCard = newBoard[adjIndex]
      if (!adjacentCard || adjacentCard.owner === placedCard.owner) return

      const direction = getDirection(placedIndex, adjIndex)
      if (!direction) return

      if (canFlipCardWithShield(placedCard, adjacentCard, direction)) {
        // Remove shield if present, or flip the card
        if (adjacentCard.shielded) {
          newBoard[adjIndex] = { ...adjacentCard, shielded: false }
        } else {
          newBoard[adjIndex] = { ...adjacentCard, owner: placedCard.owner }
        }
      }
    })
  }

  // Then execute onPlay abilities (all abilities are onPlay now)
  newBoard = executeAbility(newBoard, placedIndex, placedCard)

  return newBoard
}

// AI Decision Making
export type AIMove = {
  cardIndex: number
  boardPosition: number
  score: number
}

// Evaluate the potential score gain from placing a card at a position
export function evaluateMove(board: (DuckCard | null)[], card: DuckCard, position: number): number {
  if (board[position] !== null) return -1000 // Invalid move

  let score = 0
  const adjacentPositions = getAdjacentPositions(position)

  // Calculate potential flips
  adjacentPositions.forEach(adjIndex => {
    const adjacentCard = board[adjIndex]
    if (!adjacentCard || adjacentCard.owner === card.owner) return

    const direction = getDirection(position, adjIndex)
    if (!direction) return

    if (canFlipCard(card, adjacentCard, direction)) {
      score += 10 // Base score for flipping a card

      // Bonus for flipping high-stat cards
      const avgStat = (adjacentCard.top + adjacentCard.right + adjacentCard.bottom + adjacentCard.left) / 4
      score += avgStat
    }
  })

  // Bonus for center positions (more strategic)
  if (position === 4) score += 5 // Center
  else if ([1, 3, 5, 7].includes(position)) score += 3 // Edges
  else score += 1 // Corners

  // Consider card's own stats
  const cardAvgStat = (card.top + card.right + card.bottom + card.left) / 4
  score += cardAvgStat * 0.5

  // Bonus for ability potential
  if (card.ability.trigger === 'onPlay') {
    score += 5
  }

  return score
}

// Find the best move for AI
export function findBestAIMove(board: (DuckCard | null)[], hand: DuckCard[]): AIMove | null {
  if (hand.length === 0) return null

  let bestMove: AIMove | null = null
  let bestScore = -Infinity

  hand.forEach((card, cardIndex) => {
    for (let position = 0; position < 9; position++) {
      if (board[position] !== null) continue

      const score = evaluateMove(board, card, position)

      if (score > bestScore) {
        bestScore = score
        bestMove = {
          cardIndex,
          boardPosition: position,
          score
        }
      }
    }
  })

  return bestMove
}

// Advanced AI that considers multiple moves ahead
export function findBestAIMoveAdvanced(board: (DuckCard | null)[], hand: DuckCard[], depth: number = 2): AIMove | null {
  if (hand.length === 0) return null

  let bestMove: AIMove | null = null
  let bestScore = -Infinity

  hand.forEach((card, cardIndex) => {
    for (let position = 0; position < 9; position++) {
      if (board[position] !== null) continue

      // Simulate the move
      const newBoard = [...board]
      newBoard[position] = { ...card, owner: 'enemy' }
      const boardAfterFlip = executeCardFlipWithAbilities(newBoard, position, { ...card, owner: 'enemy' })

      let score = evaluateMove(board, card, position)

      // Look ahead if depth > 1
      if (depth > 1) {
        const futureScore = evaluateBoardPosition(boardAfterFlip, 'enemy')
        score += futureScore * 0.3 // Weight future position less
      }

      if (score > bestScore) {
        bestScore = score
        bestMove = {
          cardIndex,
          boardPosition: position,
          score
        }
      }
    }
  })

  return bestMove
}

// Evaluate overall board position for a player
function evaluateBoardPosition(board: (DuckCard | null)[], player: 'player' | 'enemy'): number {
  let score = 0
  const { playerScore, enemyScore } = calculateScores(board)

  if (player === 'player') {
    score = playerScore - enemyScore
  } else {
    score = enemyScore - playerScore
  }

  return score * 10
}

// Initialize a new game state
export function initializeGame(): GameState {
  return {
    board: Array(9).fill(null),
    playerHand: [],
    enemyHand: [],
    currentTurn: 'player',
    gameStatus: 'playing',
    playerScore: 0,
    enemyScore: 0
  }
}